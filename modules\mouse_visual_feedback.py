#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鼠标移动视觉反馈模块
功能：为鼠标移动操作提供视觉反馈效果

核心特性：
1. 鼠标轨迹显示
2. 移动动画效果
3. 点击位置高亮
4. 可配置的视觉效果

版本：1.0.0
作者：AI助手
创建时间：2025-01-28
"""

import tkinter as tk
import threading
import time
import math
from typing import Tuple, Optional, List
import pyautogui
import logging


class MouseVisualFeedback:
    """鼠标移动视觉反馈类"""
    
    def __init__(self, config: dict = None):
        """初始化视觉反馈"""
        self.logger = logging.getLogger(__name__)
        
        # 默认配置
        self.config = config or {
            "trail_enabled": True,
            "trail_color": "#FF0000",
            "trail_width": 3,
            "trail_duration": 2.0,
            "click_highlight_enabled": True,
            "click_highlight_color": "#00FF00",
            "click_highlight_size": 20,
            "click_highlight_duration": 1.0,
            "animation_enabled": True,
            "animation_steps": 20,
            "show_coordinates": True
        }
        
        # 视觉效果窗口
        self.trail_windows: List[tk.Toplevel] = []
        self.highlight_windows: List[tk.Toplevel] = []
        
        # 线程控制
        self.cleanup_thread = None
        self.is_running = True
        
        self.logger.info("✅ 鼠标视觉反馈模块初始化完成")
    
    def enhanced_move_to(self, start_pos: Tuple[int, int], end_pos: Tuple[int, int], 
                        duration: float = 0.3, element_name: str = "") -> bool:
        """增强的鼠标移动，带视觉反馈"""
        try:
            self.logger.info(f"🖱️ 开始移动鼠标到 {element_name}: {start_pos} -> {end_pos}")
            
            # 显示起始点高亮
            if self.config.get("click_highlight_enabled", True):
                self._show_position_highlight(start_pos, "start")
            
            # 执行带轨迹的移动
            if self.config.get("animation_enabled", True):
                self._animated_move_with_trail(start_pos, end_pos, duration)
            else:
                # 简单移动
                pyautogui.moveTo(end_pos[0], end_pos[1], duration=duration)
            
            # 显示终点高亮
            if self.config.get("click_highlight_enabled", True):
                self._show_position_highlight(end_pos, "end")
            
            self.logger.info(f"✅ 鼠标移动完成: {element_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 鼠标移动失败: {e}")
            return False
    
    def _animated_move_with_trail(self, start_pos: Tuple[int, int], 
                                 end_pos: Tuple[int, int], duration: float):
        """带轨迹的动画移动"""
        try:
            steps = self.config.get("animation_steps", 20)
            step_duration = duration / steps
            
            start_x, start_y = start_pos
            end_x, end_y = end_pos
            
            # 计算移动路径
            for i in range(steps + 1):
                progress = i / steps
                
                # 使用缓动函数使移动更自然
                eased_progress = self._ease_in_out_cubic(progress)
                
                current_x = start_x + (end_x - start_x) * eased_progress
                current_y = start_y + (end_y - start_y) * eased_progress
                
                # 移动鼠标
                pyautogui.moveTo(int(current_x), int(current_y), duration=0)
                
                # 显示轨迹点
                if self.config.get("trail_enabled", True) and i % 3 == 0:
                    self._show_trail_point((int(current_x), int(current_y)))
                
                time.sleep(step_duration)
                
        except Exception as e:
            self.logger.error(f"❌ 动画移动失败: {e}")
            # 回退到简单移动
            pyautogui.moveTo(end_pos[0], end_pos[1], duration=duration)
    
    def _ease_in_out_cubic(self, t: float) -> float:
        """三次缓动函数"""
        if t < 0.5:
            return 4 * t * t * t
        else:
            return 1 - pow(-2 * t + 2, 3) / 2
    
    def _show_trail_point(self, pos: Tuple[int, int]):
        """显示轨迹点"""
        try:
            if not self.config.get("trail_enabled", True):
                return
                
            # 创建轨迹点窗口
            trail_window = tk.Toplevel()
            trail_window.overrideredirect(True)
            trail_window.attributes('-topmost', True)
            trail_window.attributes('-alpha', 0.7)
            
            # 设置窗口位置和大小
            size = self.config.get("trail_width", 3) * 2
            trail_window.geometry(f"{size}x{size}+{pos[0]-size//2}+{pos[1]-size//2}")
            
            # 创建圆形轨迹点
            canvas = tk.Canvas(trail_window, width=size, height=size, 
                             highlightthickness=0, bg='white')
            canvas.pack()
            
            color = self.config.get("trail_color", "#FF0000")
            canvas.create_oval(0, 0, size, size, fill=color, outline=color)
            
            # 添加到轨迹窗口列表
            self.trail_windows.append(trail_window)
            
            # 启动清理线程
            self._start_cleanup_thread()
            
        except Exception as e:
            self.logger.debug(f"显示轨迹点失败: {e}")
    
    def _show_position_highlight(self, pos: Tuple[int, int], position_type: str = ""):
        """显示位置高亮"""
        try:
            if not self.config.get("click_highlight_enabled", True):
                return
                
            # 创建高亮窗口
            highlight_window = tk.Toplevel()
            highlight_window.overrideredirect(True)
            highlight_window.attributes('-topmost', True)
            highlight_window.attributes('-alpha', 0.8)
            
            # 设置窗口位置和大小
            size = self.config.get("click_highlight_size", 20)
            highlight_window.geometry(f"{size}x{size}+{pos[0]-size//2}+{pos[1]-size//2}")
            
            # 创建高亮效果
            canvas = tk.Canvas(highlight_window, width=size, height=size, 
                             highlightthickness=0, bg='white')
            canvas.pack()
            
            # 根据位置类型选择颜色
            if position_type == "start":
                color = "#0000FF"  # 蓝色表示起始点
            elif position_type == "end":
                color = self.config.get("click_highlight_color", "#00FF00")  # 绿色表示终点
            else:
                color = self.config.get("click_highlight_color", "#00FF00")
            
            # 创建圆形高亮
            canvas.create_oval(2, 2, size-2, size-2, fill=color, outline=color, width=2)
            
            # 显示坐标（如果启用）
            if self.config.get("show_coordinates", True):
                coord_text = f"({pos[0]},{pos[1]})"
                canvas.create_text(size//2, size//2, text=coord_text, 
                                 fill="white", font=("Arial", 8, "bold"))
            
            # 添加到高亮窗口列表
            self.highlight_windows.append(highlight_window)
            
            # 启动清理线程
            self._start_cleanup_thread()
            
        except Exception as e:
            self.logger.debug(f"显示位置高亮失败: {e}")
    
    def _start_cleanup_thread(self):
        """启动清理线程"""
        if self.cleanup_thread is None or not self.cleanup_thread.is_alive():
            self.cleanup_thread = threading.Thread(target=self._cleanup_windows, daemon=True)
            self.cleanup_thread.start()
    
    def _cleanup_windows(self):
        """清理过期的视觉效果窗口"""
        try:
            # 等待一段时间后清理轨迹点
            time.sleep(self.config.get("trail_duration", 2.0))
            
            # 清理轨迹窗口
            for window in self.trail_windows[:]:
                try:
                    window.destroy()
                    self.trail_windows.remove(window)
                except:
                    pass
            
            # 等待一段时间后清理高亮
            time.sleep(self.config.get("click_highlight_duration", 1.0))
            
            # 清理高亮窗口
            for window in self.highlight_windows[:]:
                try:
                    window.destroy()
                    self.highlight_windows.remove(window)
                except:
                    pass
                    
        except Exception as e:
            self.logger.debug(f"清理视觉效果窗口失败: {e}")
    
    def show_click_effect(self, pos: Tuple[int, int], element_name: str = ""):
        """显示点击效果"""
        try:
            self.logger.info(f"🎯 显示点击效果: {element_name} at {pos}")
            
            # 显示点击高亮
            self._show_position_highlight(pos, "click")
            
            # 可以添加更多点击效果，如波纹动画等
            
        except Exception as e:
            self.logger.error(f"❌ 显示点击效果失败: {e}")
    
    def cleanup_all(self):
        """清理所有视觉效果"""
        try:
            self.is_running = False
            
            # 清理所有轨迹窗口
            for window in self.trail_windows[:]:
                try:
                    window.destroy()
                except:
                    pass
            self.trail_windows.clear()
            
            # 清理所有高亮窗口
            for window in self.highlight_windows[:]:
                try:
                    window.destroy()
                except:
                    pass
            self.highlight_windows.clear()
            
            self.logger.info("✅ 所有视觉效果已清理")
            
        except Exception as e:
            self.logger.error(f"❌ 清理视觉效果失败: {e}")
    
    def update_config(self, new_config: dict):
        """更新配置"""
        self.config.update(new_config)
        self.logger.info("✅ 视觉反馈配置已更新")


# 全局实例
_mouse_feedback_instance = None

def get_mouse_feedback_instance(config: dict = None) -> MouseVisualFeedback:
    """获取鼠标视觉反馈实例（单例模式）"""
    global _mouse_feedback_instance
    if _mouse_feedback_instance is None:
        _mouse_feedback_instance = MouseVisualFeedback(config)
    return _mouse_feedback_instance
