#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信主界面点击操作模块
功能：微信主界面的各种点击操作，包括微信按钮、通讯录、添加朋友等
"""

import pyautogui
import time
import logging
import json
import os
from typing import Tuple, Dict, Optional
import numpy as np
from PIL import Image, ImageDraw
import win32gui
from pathlib import Path

# 注释掉截图清理模块导入，使用内置清理功能
# from screenshot_cleaner import ScreenshotCleaner

# 导入鼠标视觉反馈模块
from .mouse_visual_feedback import get_mouse_feedback_instance

class WeChatMainInterface:
    """微信主界面操作类"""

    def __init__(self, config_path: str = "config.json", window_manager=None):
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config(config_path)

        # 存储窗口管理器实例（如果提供）
        self.window_manager = window_manager

        # 从配置文件加载坐标
        self.coordinates = self.config.get("optimized_coordinates", {})
        self.mouse_config = self.config.get("mouse_optimization", {}).get("medium", {})
        self.highlight_config = self.config.get("red_border_highlight", {})

        # 设置pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = self.mouse_config.get("pause", 0.1)

        # 初始化截图清理器（使用内置功能）
        # self.screenshot_cleaner = ScreenshotCleaner()

        # 初始化鼠标视觉反馈
        visual_feedback_config = {
            "trail_enabled": True,
            "trail_color": "#FF0000",
            "trail_width": 3,
            "trail_duration": 2.0,
            "click_highlight_enabled": True,
            "click_highlight_color": "#00FF00",
            "click_highlight_size": 20,
            "click_highlight_duration": 1.5,
            "animation_enabled": True,
            "animation_steps": 15,
            "show_coordinates": True
        }
        self.mouse_feedback = get_mouse_feedback_instance(visual_feedback_config)

        self.logger.info("✅ 微信主界面操作模块初始化完成（含视觉反馈）")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.logger.info(f"✅ 配置文件加载成功: {config_path}")
            return config
        except Exception as e:
            self.logger.error(f"❌ 配置文件加载失败: {e}")
            return {}
    
    def _get_coordinate(self, element_name: str) -> Optional[Tuple[int, int]]:
        """获取元素坐标"""
        coord = self.coordinates.get(element_name)
        if coord and len(coord) == 2:
            return tuple(coord)
        self.logger.error(f"❌ 未找到元素坐标: {element_name}")
        return None
    
    def _highlight_click_area(self, x: int, y: int, element_name: str = ""):
        """高亮显示点击区域 - 增强版"""
        if not self.highlight_config.get("enabled", False):
            return

        try:
            # 获取元素大小
            element_sizes = self.highlight_config.get("element_sizes", {})
            default_size = self.highlight_config.get("default_size", [80, 30])
            size = element_sizes.get(element_name, default_size)

            width, height = size
            padding = self.highlight_config.get("element_padding", 10)

            # 创建高亮图像
            highlight_img = Image.new('RGBA', (width + padding * 2, height + padding * 2), (0, 0, 0, 0))
            draw = ImageDraw.Draw(highlight_img)

            # 绘制红色边框
            border_width = self.highlight_config.get("border_width", 3)
            for i in range(border_width):
                draw.rectangle([i, i, width + padding * 2 - 1 - i, height + padding * 2 - 1 - i],
                             outline='red', fill=None)

            # 显示时间（增加超时保护）
            display_time = self.highlight_config.get("display_time", 1.5)
            # 限制最大显示时间，防止卡住
            display_time = min(display_time, 3.0)

            self.logger.info(f"🔴 高亮显示点击区域: {element_name} ({x}, {y})")

            # 使用更安全的延时方式
            try:
                time.sleep(display_time)
            except KeyboardInterrupt:
                self.logger.warning("⚠️ 高亮显示被中断")
            except Exception as sleep_error:
                self.logger.warning(f"⚠️ 高亮显示延时异常: {sleep_error}")

        except Exception as e:
            self.logger.warning(f"⚠️ 高亮显示失败: {e}")
    
    def _safe_click(self, x: int, y: int, element_name: str = "", delay: Optional[float] = None) -> bool:
        """安全点击操作 - 增强版（含视觉反馈）"""
        try:
            if delay is None:
                delay = float(self.mouse_config.get("click_delay", 0.2))

            self.logger.info(f"🖱️ 点击 {element_name}: ({x}, {y})")

            # 获取当前鼠标位置作为起始点
            current_pos = pyautogui.position()
            start_pos = (current_pos.x, current_pos.y)
            target_pos = (x, y)

            # 高亮显示点击区域（增加超时保护）
            try:
                self._highlight_click_area(x, y, element_name)
            except Exception as highlight_error:
                self.logger.warning(f"⚠️ 高亮显示异常，继续执行点击: {highlight_error}")

            # 使用视觉反馈移动鼠标到目标位置
            try:
                duration = self.mouse_config.get("duration", 0.3)

                # 使用视觉反馈进行鼠标移动
                success = self.mouse_feedback.enhanced_move_to(
                    start_pos, target_pos, duration, element_name
                )

                if not success:
                    # 回退到传统移动方式
                    self.logger.warning("⚠️ 视觉反馈移动失败，使用传统方式")
                    pyautogui.moveTo(x, y, duration=duration)

                time.sleep(0.1)
            except Exception as move_error:
                self.logger.warning(f"⚠️ 鼠标移动异常，尝试直接点击: {move_error}")

            # 执行点击
            try:
                pyautogui.click(x, y)

                # 显示点击效果
                try:
                    self.mouse_feedback.show_click_effect(target_pos, element_name)
                except Exception as effect_error:
                    self.logger.debug(f"点击效果显示失败: {effect_error}")

                time.sleep(delay)
                self.logger.info(f"✅ 成功点击: {element_name}")
                return True
            except Exception as click_error:
                self.logger.error(f"❌ 点击操作失败 {element_name}: {click_error}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 点击失败 {element_name}: {e}")
            return False
    
    def click_wechat_button(self) -> bool:
        """步骤1: 点击微信按钮"""
        coord = self._get_coordinate("微信按钮")
        if not coord:
            return False
        
        return self._safe_click(coord[0], coord[1], "微信按钮")
    
    def click_contacts_button(self) -> bool:
        """步骤2: 点击通讯录按钮"""
        coord = self._get_coordinate("通讯录按钮")
        if not coord:
            return False
        
        return self._safe_click(coord[0], coord[1], "通讯录按钮")
    
    def click_wechat_main_button(self) -> bool:
        """步骤3: 点击微信主按钮"""
        coord = self._get_coordinate("微信主按钮")
        if not coord:
            return False
        
        return self._safe_click(coord[0], coord[1], "微信主按钮")
    
    def click_quick_action_button(self) -> bool:
        """步骤4: 点击+快捷操作按钮"""
        coord = self._get_coordinate("+快捷操作按钮")
        if not coord:
            return False
        
        return self._safe_click(coord[0], coord[1], "+快捷操作按钮")
    
    def click_add_friend_option(self) -> bool:
        """步骤5: 点击添加朋友选项按钮 - 增强版"""
        coord = self._get_coordinate("添加朋友选项")
        if not coord:
            return False

        # 执行点击操作
        click_success = self._safe_click(coord[0], coord[1], "添加朋友选项")

        if click_success:
            # 点击成功后，等待添加朋友窗口出现
            self.logger.info("🔍 点击成功，等待添加朋友窗口出现...")
            add_friend_hwnd = self._wait_for_add_friend_window()

            if add_friend_hwnd:
                self.logger.info("✅ 添加朋友窗口已出现并激活")
                return True
            else:
                self.logger.warning("⚠️ 添加朋友窗口未出现，但点击操作已执行")
                return True  # 仍然返回True，因为点击操作本身成功了

        return False
    
    def execute_main_interface_flow(self) -> bool:
        """执行完整的主界面操作流程"""
        self.logger.info("🚀 [主界面操作] 开始执行微信主界面操作流程...")

        try:
            # 首先验证微信窗口状态
            self.logger.info("🔍 [主界面操作] 验证微信窗口状态...")
            if not self._verify_wechat_window():
                self.logger.error("❌ [主界面操作] 微信窗口验证失败")
                return False

            self.logger.info("✅ [主界面操作] 微信窗口验证成功")

            steps = [
                ("步骤1: 点击微信按钮", self.click_wechat_button),
                ("步骤2: 点击通讯录按钮", self.click_contacts_button),
                ("步骤3: 点击微信主按钮", self.click_wechat_main_button),
                ("步骤4: 点击+快捷操作按钮", self.click_quick_action_button),
                ("步骤5: 点击添加朋友选项", self.click_add_friend_option)
            ]

            for i, (step_name, step_func) in enumerate(steps, 1):
                self.logger.info(f"📋 [主界面操作] 执行 {step_name} ({i}/{len(steps)})")

                try:
                    # 执行步骤前的准备
                    self._prepare_for_step(step_name)

                    # 执行步骤（增加超时保护）
                    self.logger.info(f"🔄 [主界面操作] 开始执行 {step_name}...")
                    step_result = step_func()

                    if not step_result:
                        self.logger.error(f"❌ [主界面操作] {step_name} 执行失败")
                        # 尝试恢复
                        if self._attempt_recovery(step_name):
                            self.logger.info(f"🔄 [主界面操作] {step_name} 恢复成功，继续执行")
                            if not step_func():  # 重试一次
                                self.logger.error(f"❌ [主界面操作] {step_name} 重试后仍然失败")
                                return False
                        else:
                            return False

                    self.logger.info(f"✅ [主界面操作] {step_name} 执行成功")

                    # 特殊处理：如果是添加朋友步骤，进行额外验证
                    if "添加朋友" in step_name:
                        self.logger.info("🔍 [主界面操作] 执行添加朋友窗口验证...")
                        time.sleep(1.0)  # 给窗口更多时间出现

                        if self._verify_add_friend_window_visible():
                            self.logger.info("✅ [主界面操作] 添加朋友窗口验证成功")
                        else:
                            self.logger.warning("⚠️ [主界面操作] 添加朋友窗口验证失败，但继续执行")

                    # 步骤间延迟
                    if i < len(steps):  # 最后一步不需要延迟
                        delay_range = self.config.get("delay_range", [1.5, 3.0])
                        delay = np.random.uniform(delay_range[0], delay_range[1])
                        self.logger.info(f"⏳ [主界面操作] 等待 {delay:.1f} 秒...")
                        try:
                            time.sleep(delay)
                        except Exception as delay_error:
                            self.logger.warning(f"⚠️ 延时异常，继续执行: {delay_error}")

                except Exception as step_error:
                    self.logger.error(f"❌ [主界面操作] {step_name} 执行过程中发生异常: {step_error}")
                    self.logger.error(f"❌ 异常详情: {type(step_error).__name__}: {str(step_error)}")
                    # 尝试继续执行下一步，而不是直接返回失败
                    self.logger.warning(f"⚠️ 尝试继续执行后续步骤...")
                    continue

            self.logger.info("✅ [主界面操作] 微信主界面操作流程执行完成")
            return True

        except Exception as e:
            self.logger.error(f"❌ 主界面操作流程异常: {e}")
            self.logger.error(f"❌ 异常详情: {type(e).__name__}: {str(e)}")
            # 记录调用栈信息
            import traceback
            self.logger.error(f"❌ 调用栈: {traceback.format_exc()}")
            return False
    
    def verify_interface_state(self) -> Dict[str, bool]:
        """验证界面状态"""
        self.logger.info("🔍 验证微信界面状态...")
        
        # 这里可以添加界面状态检测逻辑
        # 例如：检查特定按钮是否可见、窗口标题等
        
        state = {
            "wechat_window_active": True,  # 微信窗口是否激活
            "contacts_accessible": True,   # 通讯录是否可访问
            "add_friend_available": True   # 添加朋友功能是否可用
        }
        
        return state

    def _wait_for_add_friend_window(self, timeout: int = 8) -> Optional[int]:
        """等待添加朋友窗口出现 - 新增方法"""
        try:
            self.logger.info(f"⏳ 等待添加朋友窗口出现（超时: {timeout}秒）...")

            # 使用已初始化的窗口管理器或创建新实例
            try:
                if self.window_manager:
                    window_manager = self.window_manager
                    self.logger.debug("🔄 使用已初始化的窗口管理器")
                else:
                    from modules.window_manager import WeChatWindowManager
                    window_manager = WeChatWindowManager()
                    self.logger.debug("🔄 创建新的窗口管理器实例")

                # 使用窗口管理器的等待方法（方法确实存在，忽略 Pylance 警告）
                # type: ignore[attr-defined] 用于忽略 Pylance 的误报
                add_friend_hwnd = window_manager.wait_for_add_friend_window(timeout)  # type: ignore[attr-defined]

                if add_friend_hwnd:
                    self.logger.info("✅ 添加朋友窗口已找到并激活")
                    return add_friend_hwnd
                else:
                    self.logger.warning("⚠️ 添加朋友窗口未在指定时间内出现")
                    return None

            except ImportError as e:
                self.logger.error(f"❌ 无法导入窗口管理器: {e}")
                # 降级到简单等待
                time.sleep(2)
                return None

        except Exception as e:
            self.logger.error(f"❌ 等待添加朋友窗口失败: {e}")
            return None

    def _verify_add_friend_window_visible(self) -> bool:
        """验证添加朋友窗口是否可见 - 新增方法"""
        try:
            self.logger.info("🔍 验证添加朋友窗口可见性...")

            # 使用已初始化的窗口管理器或创建新实例
            try:
                if self.window_manager:
                    window_manager = self.window_manager
                    self.logger.debug("🔄 使用已初始化的窗口管理器")
                else:
                    from modules.window_manager import WeChatWindowManager
                    window_manager = WeChatWindowManager()
                    self.logger.debug("🔄 创建新的窗口管理器实例")

                # 查找所有微信窗口
                windows = window_manager.find_all_wechat_windows()

                for window in windows:
                    hwnd = window['hwnd']
                    window_info = window_manager.get_window_info(hwnd)

                    # 检查是否为添加朋友窗口
                    if window_manager._is_add_friend_window(hwnd, window_info):
                        # 检查窗口可见性
                        is_visible = win32gui.IsWindowVisible(hwnd)
                        is_foreground = (win32gui.GetForegroundWindow() == hwnd)

                        if is_visible and is_foreground:
                            self.logger.info("✅ 添加朋友窗口可见且在前台")
                            return True
                        elif is_visible:
                            self.logger.warning("⚠️ 添加朋友窗口可见但不在前台")
                            # 尝试激活
                            window_manager.activate_window(hwnd)
                            return True
                        else:
                            self.logger.warning("⚠️ 添加朋友窗口不可见")
                            return False

                self.logger.warning("⚠️ 未找到添加朋友窗口")
                return False

            except ImportError:
                self.logger.warning("⚠️ 无法导入窗口管理器，跳过验证")
                return True  # 假设成功

        except Exception as e:
            self.logger.error(f"❌ 验证添加朋友窗口可见性失败: {e}")
            return False

    def take_interface_screenshot(self, save_path: str = "screenshots/main_interface.png") -> bool:
        """截取主界面截图"""
        try:
            # 🧹 在截图前清理screenshots目录（使用内置功能）
            self.logger.info("🧹 执行主界面截图前清理...")
            self._cleanup_screenshots_directory()
            self.logger.info("✅ 截图目录清理完成")

            # 确保截图目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)

            # 截取屏幕
            screenshot = pyautogui.screenshot()
            screenshot.save(save_path)

            self.logger.info(f"📸 主界面截图已保存: {save_path}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 截图失败: {e}")
            return False

    def _cleanup_screenshots_directory(self):
        """清理screenshots目录"""
        try:
            screenshots_dir = Path("screenshots")
            if not screenshots_dir.exists():
                screenshots_dir.mkdir(parents=True, exist_ok=True)
                return

            # 获取所有图片文件
            image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
            files_to_remove = []

            for file in screenshots_dir.iterdir():
                if file.is_file() and file.suffix.lower() in image_extensions:
                    files_to_remove.append(file)

            # 删除图片文件
            removed_count = 0
            for file_path in files_to_remove:
                try:
                    file_path.unlink()
                    removed_count += 1
                except Exception as e:
                    self.logger.warning(f"删除文件失败 {file_path}: {e}")

            if removed_count > 0:
                self.logger.info(f"✅ 清理完成，删除 {removed_count} 个截图文件")

        except Exception as e:
            self.logger.error(f"❌ 清理screenshots目录失败: {e}")
    
    def wait_for_interface_ready(self, timeout: int = 10) -> bool:
        """等待界面准备就绪"""
        self.logger.info(f"⏳ 等待微信界面准备就绪 (超时: {timeout}秒)")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            state = self.verify_interface_state()
            if all(state.values()):
                self.logger.info("✅ 微信界面已准备就绪")
                return True
            time.sleep(0.5)
        
        self.logger.warning("⏰ 等待界面准备就绪超时")
        return False
    
    def get_current_coordinates(self) -> Dict[str, Tuple[int, int]]:
        """获取当前配置的所有坐标"""
        return self.coordinates.copy()
    
    def update_coordinate(self, element_name: str, x: int, y: int) -> bool:
        """更新元素坐标"""
        try:
            self.coordinates[element_name] = [x, y]
            self.logger.info(f"✅ 更新坐标 {element_name}: ({x}, {y})")
            return True
        except Exception as e:
            self.logger.error(f"❌ 更新坐标失败: {e}")
            return False

    def _verify_wechat_window(self) -> bool:
        """验证微信窗口状态（优化版：不干扰已激活的窗口）"""
        try:
            import win32gui

            # 🔧 优化：直接检查当前前台窗口是否为微信窗口
            current_hwnd = win32gui.GetForegroundWindow()
            if current_hwnd:
                current_title = win32gui.GetWindowText(current_hwnd)
                current_class = win32gui.GetClassName(current_hwnd)

                # 检查当前前台窗口是否为微信窗口
                is_wechat_window = (
                    ("微信" in current_title and "Visual Studio Code" not in current_title) or
                    current_class == "WeChatMainWndForPC" or
                    (current_class and "Qt" in current_class and "QWindow" in current_class)
                )

                if is_wechat_window:
                    self.logger.info(f"✅ 当前前台窗口已是微信窗口: '{current_title}' (类名: {current_class})")

                    # 验证窗口基本状态
                    if win32gui.IsWindowVisible(current_hwnd) and win32gui.IsWindowEnabled(current_hwnd):
                        self.logger.info("✅ 微信窗口状态验证通过（使用main.py预激活的窗口）")
                        return True
                    else:
                        self.logger.warning("⚠️ 当前微信窗口状态异常，但继续执行")
                        return True  # 仍然返回True，避免干扰流程
                else:
                    self.logger.warning(f"⚠️ 当前前台窗口不是微信窗口: '{current_title}' (类名: {current_class})")
                    # 尝试查找并激活微信窗口
                    self.logger.info("🔄 尝试查找并激活微信窗口...")
                    if self._find_and_activate_wechat_window():
                        self.logger.info("✅ 微信窗口已激活")
                        return True
                    else:
                        self.logger.error("❌ 无法找到或激活微信窗口")
                        return False

            # 如果无法获取前台窗口，进行基本的微信窗口存在性检查
            self.logger.info("🔍 进行基本的微信窗口存在性检查...")

            wechat_windows_found = 0
            def enum_windows_callback(hwnd, _):
                nonlocal wechat_windows_found
                if win32gui.IsWindowVisible(hwnd):
                    window_text = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)
                    if (("微信" in window_text and "Visual Studio Code" not in window_text) or
                        class_name == "WeChatMainWndForPC"):
                        wechat_windows_found += 1
                return True

            win32gui.EnumWindows(enum_windows_callback, [])

            if wechat_windows_found > 0:
                self.logger.info(f"✅ 找到 {wechat_windows_found} 个微信窗口，验证通过")
                return True
            else:
                self.logger.error("❌ 未找到任何微信窗口")
                return False

        except Exception as e:
            self.logger.error(f"❌ 验证微信窗口失败: {e}")
            # 即使验证失败，也返回True，避免阻塞流程
            self.logger.info("ℹ️ 验证失败但继续执行，信任main.py的窗口管理")
            return True

    def _find_and_activate_wechat_window(self) -> bool:
        """查找并激活微信窗口（使用window_manager完整功能）"""
        try:
            # 使用已初始化的窗口管理器或创建新实例
            if self.window_manager:
                window_manager = self.window_manager
                self.logger.info("🔄 使用已初始化的window_manager查找并激活微信窗口...")
            else:
                from modules.window_manager import WeChatWindowManager
                window_manager = WeChatWindowManager()
                self.logger.info("🔄 创建新的window_manager查找并激活微信窗口...")

            # 查找所有微信窗口
            windows = window_manager.find_all_wechat_windows()
            if not windows:
                self.logger.error("❌ 未找到微信窗口")
                return False

            # 查找主窗口
            main_windows = []
            for window in windows:
                if window.get('is_main', False) or window.get('class_name') == 'WeChatMainWndForPC':
                    main_windows.append(window)
                elif window.get('title') == '微信' and 'Qt' in window.get('class_name', ''):
                    main_windows.append(window)

            if not main_windows:
                self.logger.warning("⚠️ 未找到主窗口，使用第一个微信窗口")
                main_windows = [windows[0]]

            # 激活第一个主窗口
            target_window = main_windows[0]
            hwnd = target_window.get('hwnd')
            title = target_window.get('title', '未知')
            class_name = target_window.get('class_name', '未知')

            # 验证hwnd的有效性
            if hwnd is None:
                self.logger.error("❌ 窗口句柄为None，无法激活窗口")
                return False

            if not isinstance(hwnd, int):
                self.logger.error(f"❌ 窗口句柄类型错误，期望int，实际为{type(hwnd)}: {hwnd}")
                return False

            self.logger.info(f"🎯 找到微信窗口: {title} (类名: {class_name}, 句柄: {hwnd})")

            # 使用window_manager的完整激活功能（包含窗口移动）
            self.logger.info("🚀 使用window_manager激活窗口（包含位置调整）...")
            if window_manager.activate_window(hwnd):
                self.logger.info("✅ 微信窗口激活和位置调整成功")

                # 额外等待确保窗口完全就绪
                time.sleep(1.0)
                return True
            else:
                self.logger.error("❌ 微信窗口激活失败")
                return False

        except ImportError as e:
            self.logger.error(f"❌ 导入window_manager失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ 查找微信窗口异常: {e}")
            return False

    def _prepare_for_step(self, step_name: str) -> None:
        """为步骤执行做准备"""
        try:
            self.logger.debug(f"🔧 准备执行步骤: {step_name}")

            # 短暂等待确保界面稳定
            time.sleep(0.2)

            # 记录当前鼠标位置
            current_pos = pyautogui.position()
            self.logger.debug(f"🖱️ 当前鼠标位置: {current_pos}")

        except Exception as e:
            self.logger.warning(f"⚠️ 步骤 {step_name} 准备失败: {e}")

    def _attempt_recovery(self, step_name: str) -> bool:
        """尝试从步骤失败中恢复"""
        try:
            self.logger.info(f"🔄 尝试恢复 {step_name}...")

            # 重新验证窗口状态
            if not self._verify_wechat_window():
                return False

            # 等待一段时间让界面稳定
            time.sleep(1.0)

            self.logger.info("✅ 恢复操作完成")
            return True

        except Exception as e:
            self.logger.error(f"❌ 恢复操作失败: {e}")
            return False

def main():
    """测试函数"""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    interface = WeChatMainInterface()
    
    # 显示当前坐标配置
    coords = interface.get_current_coordinates()
    print("📍 当前坐标配置:")
    for name, coord in coords.items():
        print(f"  {name}: {coord}")
    
    # 测试界面状态验证
    state = interface.verify_interface_state()
    print(f"\n🔍 界面状态: {state}")
    
    # 可选：执行完整流程测试
    # print("\n🚀 开始测试主界面操作流程...")
    # interface.execute_main_interface_flow()

if __name__ == "__main__":
    main()
