#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI修复的脚本
验证停止和暂停功能是否正常工作
"""

import sys
import time
import threading
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_gui_import():
    """测试GUI模块是否能正常导入"""
    try:
        from wechat_automation_gui import WeChatAutomationGUI
        print("✅ GUI模块导入成功")
        return True
    except Exception as e:
        print(f"❌ GUI模块导入失败: {e}")
        return False

def test_gui_initialization():
    """测试GUI是否能正常初始化"""
    try:
        from wechat_automation_gui import WeChatAutomationGUI
        
        # 创建GUI实例（不显示窗口）
        gui = WeChatAutomationGUI()
        
        # 检查新增的状态变量
        assert hasattr(gui, 'is_paused'), "缺少is_paused属性"
        assert hasattr(gui, 'stop_requested'), "缺少stop_requested属性"
        
        # 检查初始状态
        assert gui.is_running == False, "初始运行状态应为False"
        assert gui.is_paused == False, "初始暂停状态应为False"
        assert gui.stop_requested == False, "初始停止请求状态应为False"
        
        print("✅ GUI初始化测试通过")
        return True
        
    except Exception as e:
        print(f"❌ GUI初始化测试失败: {e}")
        return False

def test_stop_pause_methods():
    """测试停止和暂停方法是否存在且可调用"""
    try:
        from wechat_automation_gui import WeChatAutomationGUI
        
        gui = WeChatAutomationGUI()
        
        # 测试停止方法
        gui.stop_automation()  # 应该不会报错，即使没有运行
        print("✅ 停止方法调用成功")
        
        # 测试暂停方法
        gui.pause_automation()  # 应该不会报错，即使没有运行
        print("✅ 暂停方法调用成功")
        
        # 测试重置状态方法
        gui.reset_ui_state()
        assert gui.is_running == False
        assert gui.is_paused == False
        assert gui.stop_requested == False
        print("✅ 重置状态方法测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 停止暂停方法测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试GUI修复...")
    print("=" * 50)
    
    tests = [
        ("模块导入测试", test_gui_import),
        ("GUI初始化测试", test_gui_initialization),
        ("停止暂停方法测试", test_stop_pause_methods),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 执行 {test_name}...")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！GUI修复成功")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
